#!/usr/bin/env python3
"""
Pi Bridge Admin Panel - Startup Script
"""

import uvicorn
from app.config import settings

if __name__ == "__main__":
    print("🥧 Starting Pi Bridge Admin Panel...")
    print(f"📡 Server: http://{settings.host}:{settings.port}")
    print(f"👤 Username: {settings.admin_username}")
    print("🔐 Password: (configured in settings)")
    print()
    
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug,
        log_level="info" if not settings.debug else "debug"
    )
