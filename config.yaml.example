# Pi Bridge Admin Panel Configuration

# Server settings
host: "0.0.0.0"
port: 8000
debug: false

# Authentication
admin_username: "admin"
admin_password: "your_secure_password_here"

# Bridge configuration
bridge:
  pi:
    host: "*************"
    user: "jonathan"
    ssh_key: "~/.ssh/id_ed25519"
  server:
    host: "*************"
    user: "jono"
    ssh_key: "~/.ssh/id_ed25519"

# SSH configuration for production server (legacy - use bridge.server instead)
server_host: "*************"
server_user: "jono"
server_ssh_key_path: "~/.ssh/id_ed25519"
# server_ssh_password: "password"  # Alternative to SSH key

# Network interfaces
wifi_interface: "wlan0"
ethernet_interface: "eth0"
server_ethernet_interface: "enp2s0"

# Paths
nginx_sites_path: "/etc/nginx/sites-available"
bridge_script_path: "./scripts/restore-bridge.sh"
