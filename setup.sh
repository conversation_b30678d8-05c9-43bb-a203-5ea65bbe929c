#!/bin/bash

# Pi Bridge Admin Panel Setup Script

set -e

echo "🥧 Pi Bridge Admin Panel Setup"
echo "=============================="

# Check if running on the Pi
PI_IP="*************"
CURRENT_IP=$(hostname -I | awk '{print $1}')

if [[ "$CURRENT_IP" != "$PI_IP" ]]; then
    echo "⚠️  Warning: Expected to run on Pi with IP $PI_IP, but current IP is $CURRENT_IP"
    echo "   Continuing anyway..."
fi

echo "📦 Installing system dependencies..."

# Update package list
sudo apt-get update

# Install required system packages
sudo apt-get install -y python3 python3-pip python3-venv git nginx iptables-persistent

# Install Docker if not present
if ! command -v docker &> /dev/null; then
    echo "🐳 Installing Docker..."
    curl -fsSL https://get.docker.com -o get-docker.sh
    sudo sh get-docker.sh
    sudo usermod -aG docker $USER
    rm get-docker.sh
    echo "✅ Docker installed. You may need to log out and back in for group changes to take effect."
fi

echo "🐍 Setting up Python environment..."

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements.txt

echo "🔑 Setting up SSH keys..."

# Check if SSH key exists
if [[ ! -f ~/.ssh/id_ed25519 ]]; then
    echo "🔐 Generating SSH key..."
    ssh-keygen -t ed25519 -f ~/.ssh/id_ed25519 -N ""
    echo "✅ SSH key generated at ~/.ssh/id_ed25519"
    echo ""
    echo "📋 Copy this public key to your server (*************):"
    echo "----------------------------------------"
    cat ~/.ssh/id_ed25519.pub
    echo "----------------------------------------"
    echo ""
    echo "Run this on your server:"
    echo "ssh-copy-id -i ~/.ssh/id_ed25519.pub jono@*************"
    echo ""
    read -p "Press Enter after you've copied the SSH key to the server..."
else
    echo "✅ SSH key already exists at ~/.ssh/id_ed25519"
fi

echo "🌐 Testing connectivity..."

# Test SSH to server
if ssh -i ~/.ssh/id_ed25519 -o ConnectTimeout=5 -o BatchMode=yes jono@************* 'echo "SSH connection successful"' 2>/dev/null; then
    echo "✅ SSH connection to server successful"
else
    echo "❌ SSH connection to server failed"
    echo "   Please ensure:"
    echo "   1. Server is running and accessible at *************"
    echo "   2. SSH key has been copied to the server"
    echo "   3. SSH service is running on the server"
fi

echo "🔧 Configuring bridge..."

# Make bridge script executable
chmod +x scripts/restore-bridge.sh

# Run bridge setup (requires sudo)
echo "🌉 Setting up bridge/NAT configuration..."
sudo ./scripts/restore-bridge.sh

echo "🚀 Starting Pi Bridge Admin Panel..."

# Start the application
echo "📡 Starting server on http://$PI_IP:8000"
echo "👤 Username: admin"
echo "🔐 Password: admin (CHANGE THIS!)"
echo ""
echo "Press Ctrl+C to stop the server"
echo ""

python3 run.py
