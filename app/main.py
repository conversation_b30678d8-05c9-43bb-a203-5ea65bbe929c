from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from app.config import settings
from app.routers import dashboard, bridge, docker, nginx, cloudflare

# Create FastAPI app
app = FastAPI(
    title="Pi Bridge Admin Panel",
    description="A lightweight self-hosted admin interface for managing and monitoring a Raspberry Pi-based Wi-Fi-to-Ethernet NAT bridge",
    version="1.0.0",
    debug=settings.debug
)

# Mount static files
app.mount("/static", StaticFiles(directory="static"), name="static")

# Include routers
app.include_router(dashboard.router, tags=["dashboard"])
app.include_router(bridge.router, tags=["bridge"])
app.include_router(docker.router, tags=["docker"])
app.include_router(nginx.router, tags=["nginx"])
app.include_router(cloudflare.router, tags=["cloudflare"])

# Health check endpoint
@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "pi-bridge-admin"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
