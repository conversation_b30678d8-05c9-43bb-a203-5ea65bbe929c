import os
import yaml
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """Application settings"""
    
    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # Authentication
    admin_username: str = "admin"
    admin_password: str = "admin"
    
    # SSH configuration for production server
    server_host: str = "*************"
    server_user: str = "jono"
    server_ssh_key_path: Optional[str] = None
    server_ssh_password: Optional[str] = None
    
    # Network interfaces
    wifi_interface: str = "wlan0"
    ethernet_interface: str = "eth0"
    server_ethernet_interface: str = "enp2s0"
    
    # Paths
    nginx_sites_path: str = "/etc/nginx/sites-available"
    bridge_script_path: str = "./scripts/restore-bridge.sh"
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


def load_config() -> Settings:
    """Load configuration from environment variables and config file"""
    settings = Settings()
    
    # Try to load from config.yaml if it exists
    config_file = "config.yaml"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
            for key, value in config_data.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)
    
    return settings


# Global settings instance
settings = load_config()
