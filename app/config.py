import os
import yaml
from typing import Optional, Dict, Any
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings
from pydantic import BaseModel


class BridgeNodeConfig(BaseModel):
    """Configuration for a bridge node (Pi or Server)"""
    host: str
    user: str
    ssh_key: str


class BridgeConfig(BaseModel):
    """Bridge configuration"""
    pi: BridgeNodeConfig
    server: BridgeNodeConfig


class Settings(BaseSettings):
    """Application settings"""

    # Server configuration
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False

    # Authentication
    admin_username: str = "admin"
    admin_password: str = "admin"

    # Bridge configuration (new structured format)
    bridge: Optional[BridgeConfig] = None

    # SSH configuration for production server (legacy - for backward compatibility)
    server_host: str = "*************"
    server_user: str = "jono"
    server_ssh_key_path: Optional[str] = None
    server_ssh_password: Optional[str] = None

    # Bridge configuration via environment variables
    bridge_pi_host: str = "*************"
    bridge_pi_user: str = "jonathan"
    bridge_pi_ssh_key: str = "~/.ssh/id_ed25519"
    bridge_server_host: str = "*************"
    bridge_server_user: str = "jono"
    bridge_server_ssh_key: str = "~/.ssh/id_ed25519"

    # Network interfaces
    wifi_interface: str = "wlan0"
    ethernet_interface: str = "eth0"
    server_ethernet_interface: str = "enp2s0"

    # Paths
    nginx_sites_path: str = "/etc/nginx/sites-available"
    bridge_script_path: str = "./scripts/restore-bridge.sh"

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    def get_server_config(self) -> Dict[str, Any]:
        """Get server configuration, preferring bridge config over legacy"""
        if self.bridge:
            return {
                "host": self.bridge.server.host,
                "user": self.bridge.server.user,
                "ssh_key": os.path.expanduser(self.bridge.server.ssh_key),
                "password": None
            }
        else:
            # Fall back to environment variables or legacy config
            return {
                "host": self.bridge_server_host or self.server_host,
                "user": self.bridge_server_user or self.server_user,
                "ssh_key": os.path.expanduser(self.bridge_server_ssh_key or self.server_ssh_key_path or "~/.ssh/id_rsa"),
                "password": self.server_ssh_password
            }

    def get_pi_config(self) -> Dict[str, Any]:
        """Get Pi configuration"""
        if self.bridge:
            return {
                "host": self.bridge.pi.host,
                "user": self.bridge.pi.user,
                "ssh_key": os.path.expanduser(self.bridge.pi.ssh_key)
            }
        else:
            # Fall back to environment variables
            return {
                "host": self.bridge_pi_host,
                "user": self.bridge_pi_user,
                "ssh_key": os.path.expanduser(self.bridge_pi_ssh_key)
            }


def load_config() -> Settings:
    """Load configuration from environment variables and config file"""
    settings = Settings()

    # Try to load from config.yaml if it exists
    config_file = "config.yaml"
    if os.path.exists(config_file):
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)

            # Handle bridge configuration specially
            if 'bridge' in config_data:
                bridge_data = config_data['bridge']
                settings.bridge = BridgeConfig(
                    pi=BridgeNodeConfig(**bridge_data['pi']),
                    server=BridgeNodeConfig(**bridge_data['server'])
                )
                # Remove bridge from config_data to avoid setting it twice
                del config_data['bridge']

            # Set other configuration values
            for key, value in config_data.items():
                if hasattr(settings, key):
                    setattr(settings, key, value)

    return settings


# Global settings instance
settings = load_config()
