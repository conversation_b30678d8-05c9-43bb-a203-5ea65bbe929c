import psutil
import subprocess
import platform
from typing import List, Optional
from datetime import datetime, timedelta
from app.models.system import SystemStats, NetworkInterface, ServiceStatus


class SystemMonitor:
    """System monitoring service"""
    
    @staticmethod
    def get_system_stats() -> SystemStats:
        """Get current system statistics"""
        # CPU usage
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # Memory usage
        memory = psutil.virtual_memory()
        memory_percent = memory.percent
        
        # Disk usage (root filesystem)
        disk = psutil.disk_usage('/')
        disk_percent = (disk.used / disk.total) * 100
        
        # Uptime
        boot_time = datetime.fromtimestamp(psutil.boot_time())
        uptime = str(datetime.now() - boot_time).split('.')[0]
        
        # Load average
        load_avg = list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else [0.0, 0.0, 0.0]
        
        # Temperature (Raspberry Pi specific)
        temperature = SystemMonitor._get_cpu_temperature()
        
        return SystemStats(
            cpu_percent=cpu_percent,
            memory_percent=memory_percent,
            disk_percent=disk_percent,
            uptime=uptime,
            temperature=temperature,
            load_average=load_avg
        )
    
    @staticmethod
    def _get_cpu_temperature() -> Optional[float]:
        """Get CPU temperature (Raspberry Pi specific)"""
        try:
            with open('/sys/class/thermal/thermal_zone0/temp', 'r') as f:
                temp = int(f.read().strip()) / 1000.0
                return temp
        except (FileNotFoundError, ValueError, PermissionError):
            return None
    
    @staticmethod
    def get_network_interfaces() -> List[NetworkInterface]:
        """Get network interface information"""
        interfaces = []
        
        for interface_name, addresses in psutil.net_if_addrs().items():
            # Get IP address
            ip_address = None
            for addr in addresses:
                if addr.family == 2:  # AF_INET (IPv4)
                    ip_address = addr.address
                    break
            
            # Get interface status
            stats = psutil.net_if_stats().get(interface_name)
            is_up = stats.isup if stats else False
            
            # Get traffic stats
            io_counters = psutil.net_io_counters(pernic=True).get(interface_name)
            bytes_sent = io_counters.bytes_sent if io_counters else 0
            bytes_recv = io_counters.bytes_recv if io_counters else 0
            
            interfaces.append(NetworkInterface(
                name=interface_name,
                ip_address=ip_address,
                is_up=is_up,
                bytes_sent=bytes_sent,
                bytes_recv=bytes_recv
            ))
        
        return interfaces
    
    @staticmethod
    def get_service_status(service_names: List[str]) -> List[ServiceStatus]:
        """Get status of system services"""
        services = []
        
        for service_name in service_names:
            try:
                # Check if service is running using systemctl
                result = subprocess.run(
                    ['systemctl', 'is-active', service_name],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                is_running = result.returncode == 0
                status = result.stdout.strip() if result.stdout else "unknown"
                
                # Get PID if running
                pid = None
                if is_running:
                    try:
                        pid_result = subprocess.run(
                            ['systemctl', 'show', service_name, '--property=MainPID'],
                            capture_output=True,
                            text=True,
                            timeout=5
                        )
                        if pid_result.returncode == 0:
                            pid_line = pid_result.stdout.strip()
                            if '=' in pid_line:
                                pid_str = pid_line.split('=')[1]
                                pid = int(pid_str) if pid_str != '0' else None
                    except (ValueError, subprocess.TimeoutExpired):
                        pass
                
                services.append(ServiceStatus(
                    name=service_name,
                    is_running=is_running,
                    status=status,
                    pid=pid
                ))
                
            except subprocess.TimeoutExpired:
                services.append(ServiceStatus(
                    name=service_name,
                    is_running=False,
                    status="timeout"
                ))
            except Exception as e:
                services.append(ServiceStatus(
                    name=service_name,
                    is_running=False,
                    status=f"error: {str(e)}"
                ))
        
        return services
    
    @staticmethod
    def ping_host(host: str, timeout: int = 5) -> Optional[float]:
        """Ping a host and return latency in milliseconds"""
        try:
            result = subprocess.run(
                ['ping', '-c', '1', '-W', str(timeout), host],
                capture_output=True,
                text=True,
                timeout=timeout + 1
            )
            
            if result.returncode == 0:
                # Parse ping output for latency
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'time=' in line:
                        time_part = line.split('time=')[1].split()[0]
                        return float(time_part)
            
            return None
            
        except (subprocess.TimeoutExpired, ValueError, IndexError):
            return None
