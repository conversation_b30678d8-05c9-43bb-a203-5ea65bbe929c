import os
import subprocess
from typing import List, Dict, Any
from app.models.system import NginxSite
from app.config import settings


class NginxManager:
    """Nginx configuration management service"""
    
    @staticmethod
    def get_nginx_sites() -> List[NginxSite]:
        """Get list of Nginx site configurations"""
        sites = []
        sites_available_path = settings.nginx_sites_path
        sites_enabled_path = "/etc/nginx/sites-enabled"
        
        if not os.path.exists(sites_available_path):
            return sites
        
        try:
            for filename in os.listdir(sites_available_path):
                if filename.endswith('.conf') or not '.' in filename:
                    file_path = os.path.join(sites_available_path, filename)
                    
                    # Check if site is enabled
                    enabled_path = os.path.join(sites_enabled_path, filename)
                    is_enabled = os.path.exists(enabled_path) or os.path.islink(enabled_path)
                    
                    # Parse server names from config
                    server_names = NginxManager._parse_server_names(file_path)
                    
                    sites.append(NginxSite(
                        filename=filename,
                        path=file_path,
                        is_enabled=is_enabled,
                        server_names=server_names
                    ))
        
        except (PermissionError, FileNotFoundError):
            pass
        
        return sites
    
    @staticmethod
    def _parse_server_names(file_path: str) -> List[str]:
        """Parse server names from Nginx config file"""
        server_names = []
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
                
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line.startswith('server_name'):
                    # Extract server names
                    parts = line.replace('server_name', '').replace(';', '').strip().split()
                    server_names.extend(parts)
        
        except (FileNotFoundError, PermissionError):
            pass
        
        return server_names
    
    @staticmethod
    def get_site_config(filename: str) -> Dict[str, Any]:
        """Get content of a specific site configuration"""
        file_path = os.path.join(settings.nginx_sites_path, filename)
        
        try:
            with open(file_path, 'r') as f:
                content = f.read()
            
            return {
                "success": True,
                "filename": filename,
                "content": content,
                "path": file_path
            }
        
        except FileNotFoundError:
            return {"success": False, "error": f"File not found: {filename}"}
        except PermissionError:
            return {"success": False, "error": f"Permission denied: {filename}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def save_site_config(filename: str, content: str) -> Dict[str, Any]:
        """Save site configuration"""
        file_path = os.path.join(settings.nginx_sites_path, filename)
        
        try:
            # Backup original file
            backup_path = f"{file_path}.backup"
            if os.path.exists(file_path):
                with open(file_path, 'r') as f:
                    backup_content = f.read()
                with open(backup_path, 'w') as f:
                    f.write(backup_content)
            
            # Save new content
            with open(file_path, 'w') as f:
                f.write(content)
            
            return {"success": True, "message": f"Configuration saved: {filename}"}
        
        except PermissionError:
            return {"success": False, "error": f"Permission denied: {filename}"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def test_nginx_config() -> Dict[str, Any]:
        """Test Nginx configuration"""
        try:
            result = subprocess.run(
                ['sudo', 'nginx', '-t'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            return {
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Nginx test timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def reload_nginx() -> Dict[str, Any]:
        """Reload Nginx service"""
        try:
            result = subprocess.run(
                ['sudo', 'systemctl', 'reload', 'nginx'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            return {
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Nginx reload timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def enable_site(filename: str) -> Dict[str, Any]:
        """Enable Nginx site"""
        sites_available_path = os.path.join(settings.nginx_sites_path, filename)
        sites_enabled_path = f"/etc/nginx/sites-enabled/{filename}"
        
        if not os.path.exists(sites_available_path):
            return {"success": False, "error": f"Site not found: {filename}"}
        
        try:
            # Create symlink
            if not os.path.exists(sites_enabled_path):
                os.symlink(sites_available_path, sites_enabled_path)
            
            return {"success": True, "message": f"Site enabled: {filename}"}
        
        except PermissionError:
            return {"success": False, "error": f"Permission denied"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def disable_site(filename: str) -> Dict[str, Any]:
        """Disable Nginx site"""
        sites_enabled_path = f"/etc/nginx/sites-enabled/{filename}"
        
        try:
            if os.path.exists(sites_enabled_path) or os.path.islink(sites_enabled_path):
                os.remove(sites_enabled_path)
            
            return {"success": True, "message": f"Site disabled: {filename}"}
        
        except PermissionError:
            return {"success": False, "error": f"Permission denied"}
        except Exception as e:
            return {"success": False, "error": str(e)}
