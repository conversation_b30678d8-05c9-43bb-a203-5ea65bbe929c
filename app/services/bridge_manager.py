import subprocess
import os
from typing import List, Dict, Any
from app.models.system import BridgeStatus
from app.config import settings
from app.services.system_monitor import SystemMonitor


class BridgeManager:
    """Bridge/NAT management service"""
    
    @staticmethod
    def get_bridge_status() -> BridgeStatus:
        """Get current bridge/NAT status"""
        # Check IP forwarding
        ip_forwarding = BridgeManager._is_ip_forwarding_enabled()

        # Get MASQUERADE rules
        masquerade_rules = BridgeManager._get_masquerade_rules()

        # Ping server using the configured server host
        server_config = settings.get_server_config()
        ping_latency = SystemMonitor.ping_host(server_config["host"])

        return BridgeStatus(
            ip_forwarding_enabled=ip_forwarding,
            masquerade_rules=masquerade_rules,
            ping_latency=ping_latency
        )
    
    @staticmethod
    def _is_ip_forwarding_enabled() -> bool:
        """Check if IP forwarding is enabled"""
        try:
            with open('/proc/sys/net/ipv4/ip_forward', 'r') as f:
                return f.read().strip() == '1'
        except (FileNotFoundError, PermissionError):
            return False
    
    @staticmethod
    def _get_masquerade_rules() -> List[str]:
        """Get current MASQUERADE rules from iptables"""
        try:
            result = subprocess.run(
                ['iptables', '-t', 'nat', '-L', 'POSTROUTING', '-v', '-n'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                rules = []
                lines = result.stdout.split('\n')
                for line in lines:
                    if 'MASQUERADE' in line:
                        rules.append(line.strip())
                return rules
            
        except (subprocess.TimeoutExpired, FileNotFoundError, PermissionError):
            pass
        
        return []
    
    @staticmethod
    def enable_ip_forwarding() -> Dict[str, Any]:
        """Enable IP forwarding"""
        try:
            # Enable temporarily
            result = subprocess.run(
                ['sudo', 'sysctl', 'net.ipv4.ip_forward=1'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                # Make permanent by updating /etc/sysctl.conf
                try:
                    with open('/etc/sysctl.conf', 'r') as f:
                        content = f.read()
                    
                    if 'net.ipv4.ip_forward=1' not in content:
                        with open('/etc/sysctl.conf', 'a') as f:
                            f.write('\n# Enable IP forwarding\nnet.ipv4.ip_forward=1\n')
                except (FileNotFoundError, PermissionError):
                    pass
                
                return {"success": True, "message": "IP forwarding enabled"}
            else:
                return {"success": False, "error": result.stderr}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def disable_ip_forwarding() -> Dict[str, Any]:
        """Disable IP forwarding"""
        try:
            result = subprocess.run(
                ['sudo', 'sysctl', 'net.ipv4.ip_forward=0'],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                return {"success": True, "message": "IP forwarding disabled"}
            else:
                return {"success": False, "error": result.stderr}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def add_masquerade_rule() -> Dict[str, Any]:
        """Add MASQUERADE rule for the bridge"""
        try:
            # Add MASQUERADE rule for traffic from ethernet to wifi
            result = subprocess.run([
                'sudo', 'iptables', '-t', 'nat', '-A', 'POSTROUTING',
                '-o', settings.wifi_interface, '-j', 'MASQUERADE'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                # Save iptables rules
                save_result = subprocess.run(
                    ['sudo', 'iptables-save'],
                    capture_output=True,
                    text=True,
                    timeout=10
                )
                
                if save_result.returncode == 0:
                    try:
                        with open('/etc/iptables/rules.v4', 'w') as f:
                            f.write(save_result.stdout)
                    except (FileNotFoundError, PermissionError):
                        pass
                
                return {"success": True, "message": "MASQUERADE rule added"}
            else:
                return {"success": False, "error": result.stderr}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def remove_masquerade_rules() -> Dict[str, Any]:
        """Remove all MASQUERADE rules"""
        try:
            # Flush NAT table POSTROUTING chain
            result = subprocess.run([
                'sudo', 'iptables', '-t', 'nat', '-F', 'POSTROUTING'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                return {"success": True, "message": "MASQUERADE rules removed"}
            else:
                return {"success": False, "error": result.stderr}
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    @staticmethod
    def run_bridge_setup_script() -> Dict[str, Any]:
        """Run the bridge setup script"""
        script_path = settings.bridge_script_path
        
        if not os.path.exists(script_path):
            return {"success": False, "error": f"Bridge script not found: {script_path}"}
        
        try:
            result = subprocess.run(
                ['sudo', 'bash', script_path],
                capture_output=True,
                text=True,
                timeout=60
            )
            
            return {
                "success": result.returncode == 0,
                "exit_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
        
        except subprocess.TimeoutExpired:
            return {"success": False, "error": "Script execution timed out"}
        except Exception as e:
            return {"success": False, "error": str(e)}
