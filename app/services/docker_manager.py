import docker
from typing import List, Dict, Any
from datetime import datetime
from app.models.system import DockerContainer
from app.services.ssh_client import SSHClient


class DockerManager:
    """Docker container management service"""
    
    def __init__(self):
        try:
            self.local_client = docker.from_env()
        except Exception:
            self.local_client = None
    
    def get_local_containers(self) -> List[DockerContainer]:
        """Get Docker containers running on the Pi"""
        containers = []
        
        if not self.local_client:
            return containers
        
        try:
            for container in self.local_client.containers.list(all=True):
                # Parse ports
                ports = []
                if container.ports:
                    for private_port, public_ports in container.ports.items():
                        if public_ports:
                            for public_port in public_ports:
                                ports.append(f"{public_port['HostPort']}:{private_port}")
                        else:
                            ports.append(private_port)
                
                containers.append(DockerContainer(
                    id=container.short_id,
                    name=container.name,
                    image=container.image.tags[0] if container.image.tags else container.image.id,
                    status=container.status,
                    state=container.attrs['State']['Status'],
                    ports=ports,
                    created=datetime.fromisoformat(container.attrs['Created'].replace('Z', '+00:00'))
                ))
        
        except Exception as e:
            print(f"Error getting local containers: {e}")
        
        return containers
    
    def get_remote_containers(self) -> List[DockerContainer]:
        """Get Docker containers running on the remote server"""
        containers = []
        
        with SSHClient() as ssh:
            if ssh.connected:
                container_data = ssh.get_docker_containers()
                
                for data in container_data:
                    try:
                        # Parse created date
                        created = datetime.now()  # Fallback
                        if data.get('created'):
                            try:
                                created = datetime.strptime(data['created'], '%Y-%m-%d %H:%M:%S %z')
                            except ValueError:
                                pass
                        
                        containers.append(DockerContainer(
                            id=data['id'],
                            name=data['name'],
                            image=data['image'],
                            status=data['status'],
                            state=data['state'],
                            ports=data.get('ports', []),
                            created=created
                        ))
                    except Exception as e:
                        print(f"Error parsing container data: {e}")
        
        return containers
    
    def get_all_containers(self) -> Dict[str, List[DockerContainer]]:
        """Get containers from both Pi and remote server"""
        return {
            "pi": self.get_local_containers(),
            "server": self.get_remote_containers()
        }
    
    def local_container_action(self, container_id: str, action: str) -> Dict[str, Any]:
        """Perform action on local container"""
        if not self.local_client:
            return {"success": False, "error": "Docker client not available"}
        
        try:
            container = self.local_client.containers.get(container_id)
            
            if action == "start":
                container.start()
                return {"success": True, "message": f"Container {container_id} started"}
            elif action == "stop":
                container.stop()
                return {"success": True, "message": f"Container {container_id} stopped"}
            elif action == "restart":
                container.restart()
                return {"success": True, "message": f"Container {container_id} restarted"}
            elif action == "logs":
                logs = container.logs(tail=100).decode('utf-8')
                return {"success": True, "logs": logs}
            else:
                return {"success": False, "error": f"Invalid action: {action}"}
        
        except docker.errors.NotFound:
            return {"success": False, "error": f"Container {container_id} not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def remote_container_action(self, container_id: str, action: str) -> Dict[str, Any]:
        """Perform action on remote container"""
        with SSHClient() as ssh:
            if ssh.connected:
                return ssh.docker_action(container_id, action)
            else:
                return {"success": False, "error": "Failed to connect to remote server"}
    
    def prune_local_containers(self) -> Dict[str, Any]:
        """Prune unused local containers and images"""
        if not self.local_client:
            return {"success": False, "error": "Docker client not available"}
        
        try:
            # Prune containers
            container_prune = self.local_client.containers.prune()
            
            # Prune images
            image_prune = self.local_client.images.prune()
            
            return {
                "success": True,
                "containers_removed": container_prune.get('ContainersDeleted', 0),
                "space_reclaimed": container_prune.get('SpaceReclaimed', 0) + image_prune.get('SpaceReclaimed', 0)
            }
        
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def prune_remote_containers(self) -> Dict[str, Any]:
        """Prune unused remote containers and images"""
        with SSHClient() as ssh:
            if ssh.connected:
                result = ssh.execute_command("docker system prune -f")
                return {
                    "success": result["success"],
                    "output": result.get("stdout", ""),
                    "error": result.get("stderr", "")
                }
            else:
                return {"success": False, "error": "Failed to connect to remote server"}
