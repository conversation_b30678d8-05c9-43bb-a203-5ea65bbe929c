import paramiko
import json
import os
from typing import Optional, Dict, Any
from app.config import settings
from app.models.system import SystemStats


class SSHClient:
    """SSH client for remote server operations"""

    def __init__(self, target: str = "server"):
        """
        Initialize SSH client
        Args:
            target: "server" or "pi" - which system to connect to
        """
        self.client = None
        self.connected = False
        self.target = target

        # Get configuration for the target
        if target == "server":
            self.config = settings.get_server_config()
        elif target == "pi":
            self.config = settings.get_pi_config()
        else:
            raise ValueError(f"Invalid target: {target}. Must be 'server' or 'pi'")

    def connect(self) -> bool:
        """Connect to the target system"""
        try:
            self.client = paramiko.SSHClient()
            self.client.set_missing_host_key_policy(paramiko.AutoAddPolicy())

            # Connect using SSH key or password
            if self.config.get("ssh_key") and os.path.exists(self.config["ssh_key"]):
                self.client.connect(
                    hostname=self.config["host"],
                    username=self.config["user"],
                    key_filename=self.config["ssh_key"],
                    timeout=10
                )
            elif self.config.get("password"):
                self.client.connect(
                    hostname=self.config["host"],
                    username=self.config["user"],
                    password=self.config["password"],
                    timeout=10
                )
            else:
                # Try to use SSH agent
                self.client.connect(
                    hostname=self.config["host"],
                    username=self.config["user"],
                    timeout=10
                )

            self.connected = True
            return True

        except Exception as e:
            print(f"SSH connection to {self.target} ({self.config['host']}) failed: {e}")
            self.connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the remote server"""
        if self.client:
            self.client.close()
            self.connected = False
    
    def execute_command(self, command: str, timeout: int = 30) -> Dict[str, Any]:
        """Execute a command on the remote server"""
        if not self.connected:
            if not self.connect():
                return {"success": False, "error": "Failed to connect to server"}
        
        try:
            stdin, stdout, stderr = self.client.exec_command(command, timeout=timeout)
            
            exit_code = stdout.channel.recv_exit_status()
            stdout_data = stdout.read().decode('utf-8')
            stderr_data = stderr.read().decode('utf-8')
            
            return {
                "success": exit_code == 0,
                "exit_code": exit_code,
                "stdout": stdout_data,
                "stderr": stderr_data
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def get_remote_system_stats(self) -> Optional[SystemStats]:
        """Get system statistics from the remote server"""
        # Python script to get system stats
        script = '''
import psutil
import json
from datetime import datetime

try:
    # Get system stats
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    boot_time = datetime.fromtimestamp(psutil.boot_time())
    uptime = str(datetime.now() - boot_time).split('.')[0]
    load_avg = list(psutil.getloadavg()) if hasattr(psutil, 'getloadavg') else [0.0, 0.0, 0.0]
    
    stats = {
        "cpu_percent": cpu_percent,
        "memory_percent": memory.percent,
        "disk_percent": (disk.used / disk.total) * 100,
        "uptime": uptime,
        "load_average": load_avg
    }
    
    print(json.dumps(stats))
except Exception as e:
    print(json.dumps({"error": str(e)}))
'''
        
        result = self.execute_command(f'python3 -c "{script}"')
        
        if result["success"]:
            try:
                data = json.loads(result["stdout"])
                if "error" not in data:
                    return SystemStats(
                        cpu_percent=data["cpu_percent"],
                        memory_percent=data["memory_percent"],
                        disk_percent=data["disk_percent"],
                        uptime=data["uptime"],
                        load_average=data["load_average"]
                    )
            except (json.JSONDecodeError, KeyError):
                pass
        
        return None
    
    def get_docker_containers(self) -> list:
        """Get Docker containers from the remote server"""
        result = self.execute_command("docker ps -a --format 'table {{.ID}}\\t{{.Names}}\\t{{.Image}}\\t{{.Status}}\\t{{.State}}\\t{{.Ports}}\\t{{.CreatedAt}}'")
        
        containers = []
        if result["success"]:
            lines = result["stdout"].strip().split('\n')[1:]  # Skip header
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 6:
                        containers.append({
                            "id": parts[0],
                            "name": parts[1],
                            "image": parts[2],
                            "status": parts[3],
                            "state": parts[4],
                            "ports": parts[5].split(',') if parts[5] else [],
                            "created": parts[6] if len(parts) > 6 else ""
                        })
        
        return containers
    
    def docker_action(self, container_id: str, action: str) -> Dict[str, Any]:
        """Perform Docker action on remote server"""
        valid_actions = ["start", "stop", "restart", "logs"]
        if action not in valid_actions:
            return {"success": False, "error": f"Invalid action: {action}"}
        
        if action == "logs":
            command = f"docker logs --tail 100 {container_id}"
        else:
            command = f"docker {action} {container_id}"
        
        return self.execute_command(command)
    
    def __enter__(self):
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.disconnect()
