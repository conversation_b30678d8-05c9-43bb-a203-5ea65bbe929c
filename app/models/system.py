from typing import Dict, List, Optional
from pydantic import BaseModel
from datetime import datetime


class SystemStats(BaseModel):
    """System statistics model"""
    cpu_percent: float
    memory_percent: float
    disk_percent: float
    uptime: str
    temperature: Optional[float] = None
    load_average: List[float]


class NetworkInterface(BaseModel):
    """Network interface information"""
    name: str
    ip_address: Optional[str] = None
    is_up: bool
    bytes_sent: int = 0
    bytes_recv: int = 0


class ServiceStatus(BaseModel):
    """Service status information"""
    name: str
    is_running: bool
    status: str
    pid: Optional[int] = None


class DockerContainer(BaseModel):
    """Docker container information"""
    id: str
    name: str
    image: str
    status: str
    state: str
    ports: List[str] = []
    created: datetime


class NginxSite(BaseModel):
    """Nginx site configuration"""
    filename: str
    path: str
    is_enabled: bool
    server_names: List[str] = []


class BridgeStatus(BaseModel):
    """Bridge/NAT status"""
    ip_forwarding_enabled: bool
    masquerade_rules: List[str]
    ping_latency: Optional[float] = None


class SystemHealth(BaseModel):
    """Overall system health"""
    pi_stats: SystemStats
    server_stats: Optional[SystemStats] = None
    network_interfaces: List[NetworkInterface]
    services: List[ServiceStatus]
    bridge_status: BridgeStatus
    docker_containers: List[DockerContainer] = []
