import secrets
from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON>PBasic, HTTPBasicCredentials
from app.config import settings

security = HTTPBasic()


def authenticate(credentials: HTTPBasicCredentials = Depends(security)):
    """HTTP Basic Authentication"""
    correct_username = secrets.compare_digest(credentials.username, settings.admin_username)
    correct_password = secrets.compare_digest(credentials.password, settings.admin_password)
    
    if not (correct_username and correct_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Basic"},
        )
    
    return credentials.username
