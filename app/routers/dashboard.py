from fastapi import APIRouter, Request, Depends
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from app.auth import authenticate
from app.services.system_monitor import SystemMonitor
from app.services.ssh_client import SSHClient
from app.services.bridge_manager import BridgeManager
from app.services.docker_manager import DockerManager
from app.models.system import SystemHealth

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/", response_class=HTMLResponse)
@router.get("/dashboard", response_class=HTMLResponse)
async def dashboard(request: Request, username: str = Depends(authenticate)):
    """Main dashboard view"""
    
    # Get Pi system stats
    pi_stats = SystemMonitor.get_system_stats()
    
    # Get remote server stats
    server_stats = None
    with SSHClient() as ssh:
        if ssh.connected:
            server_stats = ssh.get_remote_system_stats()
    
    # Get network interfaces
    network_interfaces = SystemMonitor.get_network_interfaces()
    
    # Get service status
    services = SystemMonitor.get_service_status([
        'nginx', 'docker', 'cloudflared', 'ssh'
    ])
    
    # Get bridge status
    bridge_status = BridgeManager.get_bridge_status()
    
    # Get Docker containers
    docker_manager = DockerManager()
    containers = docker_manager.get_all_containers()
    
    system_health = SystemHealth(
        pi_stats=pi_stats,
        server_stats=server_stats,
        network_interfaces=network_interfaces,
        services=services,
        bridge_status=bridge_status,
        docker_containers=containers.get('pi', []) + containers.get('server', [])
    )
    
    return templates.TemplateResponse(
        "dashboard.html",
        {
            "request": request,
            "system_health": system_health,
            "pi_containers": containers.get('pi', []),
            "server_containers": containers.get('server', []),
            "username": username
        }
    )


@router.get("/api/system-stats")
async def get_system_stats(username: str = Depends(authenticate)):
    """API endpoint for system statistics (for HTMX updates)"""
    
    pi_stats = SystemMonitor.get_system_stats()
    
    server_stats = None
    with SSHClient() as ssh:
        if ssh.connected:
            server_stats = ssh.get_remote_system_stats()
    
    return {
        "pi_stats": pi_stats.dict(),
        "server_stats": server_stats.dict() if server_stats else None
    }


@router.get("/api/service-status")
async def get_service_status(username: str = Depends(authenticate)):
    """API endpoint for service status (for HTMX updates)"""
    
    services = SystemMonitor.get_service_status([
        'nginx', 'docker', 'cloudflared', 'ssh'
    ])
    
    return {"services": [service.dict() for service in services]}


@router.get("/api/bridge-status")
async def get_bridge_status(username: str = Depends(authenticate)):
    """API endpoint for bridge status (for HTMX updates)"""
    
    bridge_status = BridgeManager.get_bridge_status()
    return {"bridge_status": bridge_status.dict()}
