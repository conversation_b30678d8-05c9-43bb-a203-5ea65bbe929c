from fastapi import APIRouter, Request, Depends, Form
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from app.auth import authenticate
from app.services.bridge_manager import BridgeManager

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/bridge", response_class=HTMLResponse)
async def bridge_page(request: Request, username: str = Depends(authenticate)):
    """Bridge/NAT management page"""
    
    bridge_status = BridgeManager.get_bridge_status()
    
    return templates.TemplateResponse(
        "bridge.html",
        {
            "request": request,
            "bridge_status": bridge_status,
            "username": username
        }
    )


@router.post("/bridge/ip-forwarding/enable")
async def enable_ip_forwarding(username: str = Depends(authenticate)):
    """Enable IP forwarding"""
    result = BridgeManager.enable_ip_forwarding()
    return result


@router.post("/bridge/ip-forwarding/disable")
async def disable_ip_forwarding(username: str = Depends(authenticate)):
    """Disable IP forwarding"""
    result = BridgeManager.disable_ip_forwarding()
    return result


@router.post("/bridge/masquerade/add")
async def add_masquerade_rule(username: str = Depends(authenticate)):
    """Add MASQUERADE rule"""
    result = BridgeManager.add_masquerade_rule()
    return result


@router.post("/bridge/masquerade/remove")
async def remove_masquerade_rules(username: str = Depends(authenticate)):
    """Remove MASQUERADE rules"""
    result = BridgeManager.remove_masquerade_rules()
    return result


@router.post("/bridge/setup-script")
async def run_bridge_setup_script(username: str = Depends(authenticate)):
    """Run bridge setup script"""
    result = BridgeManager.run_bridge_setup_script()
    return result


@router.get("/bridge/status")
async def get_bridge_status(username: str = Depends(authenticate)):
    """Get current bridge status (for HTMX updates)"""
    bridge_status = BridgeManager.get_bridge_status()
    return {"bridge_status": bridge_status.dict()}
