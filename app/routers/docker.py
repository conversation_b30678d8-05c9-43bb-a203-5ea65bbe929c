from fastapi import APIRouter, Request, Depends, Form
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse
from app.auth import authenticate
from app.services.docker_manager import DockerManager

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/docker", response_class=HTMLResponse)
async def docker_page(request: Request, username: str = Depends(authenticate)):
    """Docker management page"""
    
    docker_manager = DockerManager()
    containers = docker_manager.get_all_containers()
    
    return templates.TemplateResponse(
        "docker.html",
        {
            "request": request,
            "pi_containers": containers.get('pi', []),
            "server_containers": containers.get('server', []),
            "username": username
        }
    )


@router.post("/docker/local/{container_id}/{action}")
async def local_container_action(
    container_id: str,
    action: str,
    username: str = Depends(authenticate)
):
    """Perform action on local container"""
    docker_manager = DockerManager()
    result = docker_manager.local_container_action(container_id, action)
    return result


@router.post("/docker/remote/{container_id}/{action}")
async def remote_container_action(
    container_id: str,
    action: str,
    username: str = Depends(authenticate)
):
    """Perform action on remote container"""
    docker_manager = DockerManager()
    result = docker_manager.remote_container_action(container_id, action)
    return result


@router.post("/docker/prune/local")
async def prune_local_containers(username: str = Depends(authenticate)):
    """Prune unused local containers and images"""
    docker_manager = DockerManager()
    result = docker_manager.prune_local_containers()
    return result


@router.post("/docker/prune/remote")
async def prune_remote_containers(username: str = Depends(authenticate)):
    """Prune unused remote containers and images"""
    docker_manager = DockerManager()
    result = docker_manager.prune_remote_containers()
    return result


@router.get("/docker/containers")
async def get_containers(username: str = Depends(authenticate)):
    """Get all containers (for HTMX updates)"""
    docker_manager = DockerManager()
    containers = docker_manager.get_all_containers()
    return {
        "pi_containers": [c.dict() for c in containers.get('pi', [])],
        "server_containers": [c.dict() for c in containers.get('server', [])]
    }
