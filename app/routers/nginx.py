from fastapi import APIRouter, Request, Depends, Form
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.responses import HTMLResponse
from app.auth import authenticate
from app.services.nginx_manager import NginxManager

router = APIRouter()
templates = Jinja2Templates(directory="app/templates")


@router.get("/nginx", response_class=HTMLResponse)
async def nginx_page(request: Request, username: str = Depends(authenticate)):
    """Nginx management page"""
    
    sites = NginxManager.get_nginx_sites()
    
    return templates.TemplateResponse(
        "nginx.html",
        {
            "request": request,
            "sites": sites,
            "username": username
        }
    )


@router.get("/nginx/site/{filename}")
async def get_site_config(filename: str, username: str = Depends(authenticate)):
    """Get site configuration content"""
    result = NginxManager.get_site_config(filename)
    return result


@router.post("/nginx/site/{filename}")
async def save_site_config(
    filename: str,
    content: str = Form(...),
    username: str = Depends(authenticate)
):
    """Save site configuration"""
    result = NginxManager.save_site_config(filename, content)
    return result


@router.post("/nginx/test")
async def test_nginx_config(username: str = Depends(authenticate)):
    """Test Nginx configuration"""
    result = NginxManager.test_nginx_config()
    return result


@router.post("/nginx/reload")
async def reload_nginx(username: str = Depends(authenticate)):
    """Reload Nginx service"""
    result = NginxManager.reload_nginx()
    return result


@router.post("/nginx/site/{filename}/enable")
async def enable_site(filename: str, username: str = Depends(authenticate)):
    """Enable Nginx site"""
    result = NginxManager.enable_site(filename)
    return result


@router.post("/nginx/site/{filename}/disable")
async def disable_site(filename: str, username: str = Depends(authenticate)):
    """Disable Nginx site"""
    result = NginxManager.disable_site(filename)
    return result


@router.get("/nginx/sites")
async def get_nginx_sites(username: str = Depends(authenticate)):
    """Get list of Nginx sites (for HTMX updates)"""
    sites = NginxManager.get_nginx_sites()
    return {"sites": [site.dict() for site in sites]}
