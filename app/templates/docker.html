{% extends "base.html" %}

{% block title %}Docker Management - Pi Bridge Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-gray-900">🐳 Docker Management</h2>
        <div class="flex space-x-2">
            <button class="bg-pi-blue text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                    hx-get="/docker/containers" 
                    hx-trigger="click"
                    hx-target="#containers-section"
                    hx-swap="outerHTML">
                🔄 Refresh
            </button>
        </div>
    </div>

    <!-- Containers Section -->
    <div id="containers-section">
        <!-- Pi Containers -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">🥧 Pi Containers</h3>
                <div class="flex space-x-2">
                    <button class="bg-pi-orange text-white px-3 py-1 rounded text-sm hover:bg-orange-600 transition-colors"
                            hx-post="/docker/prune/local"
                            hx-trigger="click"
                            hx-confirm="Prune unused local containers and images?">
                        🧹 Prune
                    </button>
                </div>
            </div>
            
            {% if pi_containers %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ports</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for container in pi_containers %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ container.name }}</div>
                                <div class="text-sm text-gray-500">{{ container.id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ container.image }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                           {% if container.state == 'running' %}bg-green-100 text-green-800
                                           {% elif container.state == 'exited' %}bg-red-100 text-red-800
                                           {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ container.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% for port in container.ports %}
                                    <div>{{ port }}</div>
                                {% endfor %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                {% if container.state != 'running' %}
                                <button class="text-pi-green hover:text-green-600"
                                        hx-post="/docker/local/{{ container.id }}/start"
                                        hx-trigger="click">
                                    ▶️ Start
                                </button>
                                {% endif %}
                                {% if container.state == 'running' %}
                                <button class="text-pi-red hover:text-red-600"
                                        hx-post="/docker/local/{{ container.id }}/stop"
                                        hx-trigger="click"
                                        hx-confirm="Stop container {{ container.name }}?">
                                    ⏹️ Stop
                                </button>
                                {% endif %}
                                <button class="text-pi-blue hover:text-blue-600"
                                        hx-post="/docker/local/{{ container.id }}/restart"
                                        hx-trigger="click"
                                        hx-confirm="Restart container {{ container.name }}?">
                                    🔄 Restart
                                </button>
                                <button class="text-pi-orange hover:text-orange-600"
                                        hx-post="/docker/local/{{ container.id }}/logs"
                                        hx-trigger="click"
                                        hx-target="#logs-modal-content"
                                        hx-swap="innerHTML"
                                        onclick="document.getElementById('logs-modal').classList.remove('hidden')">
                                    📋 Logs
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8 text-gray-500">
                <div class="text-4xl mb-2">📦</div>
                <p>No containers found on Pi</p>
            </div>
            {% endif %}
        </div>

        <!-- Server Containers -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold text-gray-900">🖥️ Server Containers</h3>
                <div class="flex space-x-2">
                    <button class="bg-pi-orange text-white px-3 py-1 rounded text-sm hover:bg-orange-600 transition-colors"
                            hx-post="/docker/prune/remote"
                            hx-trigger="click"
                            hx-confirm="Prune unused remote containers and images?">
                        🧹 Prune
                    </button>
                </div>
            </div>
            
            {% if server_containers %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Image</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Ports</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for container in server_containers %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ container.name }}</div>
                                <div class="text-sm text-gray-500">{{ container.id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {{ container.image }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                           {% if container.state == 'running' %}bg-green-100 text-green-800
                                           {% elif container.state == 'exited' %}bg-red-100 text-red-800
                                           {% else %}bg-yellow-100 text-yellow-800{% endif %}">
                                    {{ container.status }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                {% for port in container.ports %}
                                    <div>{{ port }}</div>
                                {% endfor %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                                {% if container.state != 'running' %}
                                <button class="text-pi-green hover:text-green-600"
                                        hx-post="/docker/remote/{{ container.id }}/start"
                                        hx-trigger="click">
                                    ▶️ Start
                                </button>
                                {% endif %}
                                {% if container.state == 'running' %}
                                <button class="text-pi-red hover:text-red-600"
                                        hx-post="/docker/remote/{{ container.id }}/stop"
                                        hx-trigger="click"
                                        hx-confirm="Stop container {{ container.name }}?">
                                    ⏹️ Stop
                                </button>
                                {% endif %}
                                <button class="text-pi-blue hover:text-blue-600"
                                        hx-post="/docker/remote/{{ container.id }}/restart"
                                        hx-trigger="click"
                                        hx-confirm="Restart container {{ container.name }}?">
                                    🔄 Restart
                                </button>
                                <button class="text-pi-orange hover:text-orange-600"
                                        hx-post="/docker/remote/{{ container.id }}/logs"
                                        hx-trigger="click"
                                        hx-target="#logs-modal-content"
                                        hx-swap="innerHTML"
                                        onclick="document.getElementById('logs-modal').classList.remove('hidden')">
                                    📋 Logs
                                </button>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8 text-gray-500">
                <div class="text-4xl mb-2">📦</div>
                <p>No containers found on server or server unreachable</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Logs Modal -->
<div id="logs-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Container Logs</h3>
            <button class="text-gray-400 hover:text-gray-600" onclick="document.getElementById('logs-modal').classList.add('hidden')">
                ✕
            </button>
        </div>
        <div id="logs-modal-content" class="bg-gray-100 p-4 rounded-lg max-h-96 overflow-y-auto">
            <!-- Logs will be loaded here -->
        </div>
    </div>
</div>

<script>
    // Handle logs display
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.elt.getAttribute('hx-post') && evt.detail.elt.getAttribute('hx-post').includes('/logs')) {
            try {
                const response = JSON.parse(evt.detail.xhr.responseText);
                const content = document.getElementById('logs-modal-content');
                
                if (response.success && response.logs) {
                    content.innerHTML = `<pre class="text-sm text-gray-800 whitespace-pre-wrap">${response.logs}</pre>`;
                } else {
                    content.innerHTML = `<div class="text-red-600">Error: ${response.error || 'Failed to retrieve logs'}</div>`;
                }
            } catch (e) {
                console.error('Error parsing logs response:', e);
            }
        }
    });
</script>
{% endblock %}
