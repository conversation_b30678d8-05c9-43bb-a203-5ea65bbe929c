{% extends "base.html" %}

{% block title %}Cloudflare Tunnel - Pi Bridge Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-gray-900">☁️ Cloudflare Tunnel Management</h2>
        <div class="flex space-x-2">
            <button class="bg-pi-blue text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                    hx-get="/cloudflare/status" 
                    hx-trigger="click"
                    hx-target="#tunnel-status"
                    hx-swap="outerHTML">
                🔄 Refresh Status
            </button>
            <button class="bg-pi-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                    hx-post="/cloudflare/restart"
                    hx-trigger="click"
                    hx-confirm="Restart cloudflared service?">
                🔄 Restart Service
            </button>
        </div>
    </div>

    <!-- Tunnel Status -->
    <div id="tunnel-status" class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Tunnel Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Service Status -->
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl mb-2">
                    {{ "🟢" if tunnel_status.is_running else "🔴" }}
                </div>
                <div class="font-medium">Service Status</div>
                <div class="text-sm text-gray-600">
                    {{ "Running" if tunnel_status.is_running else "Stopped" }}
                </div>
            </div>

            <!-- Active State -->
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl mb-2">⚡</div>
                <div class="font-medium">Active State</div>
                <div class="text-sm text-gray-600">{{ tunnel_status.active_state }}</div>
            </div>

            <!-- Sub State -->
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl mb-2">🔧</div>
                <div class="font-medium">Sub State</div>
                <div class="text-sm text-gray-600">{{ tunnel_status.sub_state }}</div>
            </div>

            <!-- Process ID -->
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl mb-2">🆔</div>
                <div class="font-medium">Process ID</div>
                <div class="text-sm text-gray-600">
                    {{ tunnel_status.pid if tunnel_status.pid else "N/A" }}
                </div>
            </div>
        </div>
    </div>

    <!-- Service Controls -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Service Controls</h3>
        <div class="flex space-x-4">
            <button class="bg-pi-orange text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors"
                    hx-post="/cloudflare/restart"
                    hx-trigger="click"
                    hx-confirm="Restart cloudflared service? This will temporarily interrupt tunnel connectivity.">
                🔄 Restart Service
            </button>
        </div>
        <p class="text-sm text-gray-600 mt-2">
            Restart the cloudflared service to apply configuration changes or resolve connectivity issues.
        </p>
    </div>

    <!-- Service Logs -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold text-gray-900">Service Logs</h3>
            <button class="bg-pi-blue text-white px-3 py-1 rounded text-sm hover:bg-blue-600 transition-colors"
                    hx-get="/cloudflare/logs"
                    hx-trigger="click"
                    hx-target="#logs-content"
                    hx-swap="innerHTML">
                🔄 Refresh Logs
            </button>
        </div>
        
        <div id="logs-content" 
             hx-get="/cloudflare/logs" 
             hx-trigger="load"
             class="bg-gray-100 p-4 rounded-lg">
            <div class="text-center text-gray-500">Loading logs...</div>
        </div>
    </div>

    <!-- Information Panel -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">ℹ️ Information</h3>
        <div class="space-y-3 text-sm text-gray-600">
            <div>
                <strong>What is Cloudflare Tunnel?</strong><br>
                Cloudflare Tunnel creates a secure connection between your Pi and Cloudflare's network, 
                allowing you to expose local services to the internet without opening firewall ports.
            </div>
            <div>
                <strong>Configuration:</strong><br>
                The tunnel configuration is typically stored in <code>/etc/cloudflared/config.yml</code> 
                or <code>~/.cloudflared/config.yml</code>.
            </div>
            <div>
                <strong>Troubleshooting:</strong><br>
                If the tunnel is not working, check the logs above for error messages. 
                Common issues include network connectivity problems or configuration errors.
            </div>
        </div>
    </div>
</div>

<script>
    // Handle logs display
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.elt.getAttribute('hx-get') === '/cloudflare/logs') {
            try {
                const response = JSON.parse(evt.detail.xhr.responseText);
                const logsContent = document.getElementById('logs-content');
                
                if (response.success) {
                    logsContent.innerHTML = `
                        <pre class="text-sm text-gray-800 whitespace-pre-wrap max-h-96 overflow-y-auto">${response.logs}</pre>
                    `;
                } else {
                    logsContent.innerHTML = `
                        <div class="text-red-600">Error loading logs: ${response.error}</div>
                    `;
                }
            } catch (e) {
                console.error('Error parsing logs response:', e);
                document.getElementById('logs-content').innerHTML = `
                    <div class="text-red-600">Error parsing logs response</div>
                `;
            }
        }
    });

    // Auto-refresh tunnel status every 30 seconds
    setInterval(() => {
        htmx.trigger(document.querySelector('[hx-get="/cloudflare/status"]'), 'click');
    }, 30000);
</script>
{% endblock %}
