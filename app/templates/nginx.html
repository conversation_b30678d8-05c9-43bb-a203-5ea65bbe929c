{% extends "base.html" %}

{% block title %}Nginx Management - Pi Bridge Admin{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Page Header -->
    <div class="flex justify-between items-center">
        <h2 class="text-3xl font-bold text-gray-900">⚙️ Nginx Management</h2>
        <div class="flex space-x-2">
            <button class="bg-pi-green text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors"
                    hx-post="/nginx/test"
                    hx-trigger="click"
                    hx-target="#test-result"
                    hx-swap="innerHTML">
                🧪 Test Config
            </button>
            <button class="bg-pi-blue text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                    hx-post="/nginx/reload"
                    hx-trigger="click"
                    hx-confirm="Reload Nginx configuration?">
                🔄 Reload Nginx
            </button>
        </div>
    </div>

    <!-- Test Result -->
    <div id="test-result"></div>

    <!-- Sites List -->
    <div class="bg-white rounded-lg shadow-md p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Available Sites</h3>
        
        {% if sites %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Filename</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server Names</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for site in sites %}
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">{{ site.filename }}</div>
                            <div class="text-sm text-gray-500">{{ site.path }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {% for server_name in site.server_names %}
                                <div class="inline-block bg-gray-100 rounded px-2 py-1 text-xs mr-1 mb-1">
                                    {{ server_name }}
                                </div>
                            {% endfor %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full
                                       {% if site.is_enabled %}bg-green-100 text-green-800{% else %}bg-gray-100 text-gray-800{% endif %}">
                                {{ "Enabled" if site.is_enabled else "Disabled" }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2">
                            <button class="text-pi-blue hover:text-blue-600"
                                    onclick="editSite('{{ site.filename }}')">
                                ✏️ Edit
                            </button>
                            {% if not site.is_enabled %}
                            <button class="text-pi-green hover:text-green-600"
                                    hx-post="/nginx/site/{{ site.filename }}/enable"
                                    hx-trigger="click"
                                    hx-confirm="Enable site {{ site.filename }}?">
                                ✅ Enable
                            </button>
                            {% else %}
                            <button class="text-pi-red hover:text-red-600"
                                    hx-post="/nginx/site/{{ site.filename }}/disable"
                                    hx-trigger="click"
                                    hx-confirm="Disable site {{ site.filename }}?">
                                ❌ Disable
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-8 text-gray-500">
            <div class="text-4xl mb-2">📄</div>
            <p>No Nginx sites found</p>
            <p class="text-sm">Check if Nginx is installed and configured</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Site Editor Modal -->
<div id="site-editor-modal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">Edit Site Configuration</h3>
            <button class="text-gray-400 hover:text-gray-600" onclick="closeSiteEditor()">
                ✕
            </button>
        </div>
        
        <div id="site-editor-content">
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Filename:</label>
                <input type="text" id="site-filename" readonly 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100">
            </div>
            
            <div class="mb-4">
                <label class="block text-sm font-medium text-gray-700 mb-2">Configuration:</label>
                <textarea id="site-content" rows="20" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-md font-mono text-sm"
                          placeholder="Loading..."></textarea>
            </div>
            
            <div class="flex justify-end space-x-2">
                <button class="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
                        onclick="closeSiteEditor()">
                    Cancel
                </button>
                <button class="bg-pi-blue text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors"
                        onclick="saveSiteConfig()">
                    💾 Save
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    let currentSiteFilename = '';

    function editSite(filename) {
        currentSiteFilename = filename;
        document.getElementById('site-filename').value = filename;
        document.getElementById('site-editor-modal').classList.remove('hidden');
        
        // Load site configuration
        fetch(`/nginx/site/${filename}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('site-content').value = data.content;
                } else {
                    document.getElementById('site-content').value = `Error loading configuration: ${data.error}`;
                }
            })
            .catch(error => {
                document.getElementById('site-content').value = `Error: ${error.message}`;
            });
    }

    function closeSiteEditor() {
        document.getElementById('site-editor-modal').classList.add('hidden');
        currentSiteFilename = '';
    }

    function saveSiteConfig() {
        const content = document.getElementById('site-content').value;
        const formData = new FormData();
        formData.append('content', content);
        
        fetch(`/nginx/site/${currentSiteFilename}`, {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                closeSiteEditor();
                // Refresh the page to show updated sites
                location.reload();
            } else {
                showToast(data.error, 'error');
            }
        })
        .catch(error => {
            showToast(`Error: ${error.message}`, 'error');
        });
    }

    // Handle test result display
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.elt.getAttribute('hx-post') === '/nginx/test') {
            try {
                const response = JSON.parse(evt.detail.xhr.responseText);
                const testResult = document.getElementById('test-result');
                
                if (response.success) {
                    testResult.innerHTML = `
                        <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
                            <strong>✅ Configuration test passed!</strong>
                            <pre class="mt-2 text-sm">${response.stdout || response.stderr}</pre>
                        </div>
                    `;
                } else {
                    testResult.innerHTML = `
                        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
                            <strong>❌ Configuration test failed!</strong>
                            <pre class="mt-2 text-sm">${response.stderr || response.error}</pre>
                        </div>
                    `;
                }
                
                // Auto-hide after 10 seconds
                setTimeout(() => {
                    testResult.innerHTML = '';
                }, 10000);
            } catch (e) {
                console.error('Error parsing test response:', e);
            }
        }
    });
</script>
{% endblock %}
