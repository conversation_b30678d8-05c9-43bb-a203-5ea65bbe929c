# Environment variables for Pi Bridge Admin Panel

# Server configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# SSH configuration
SERVER_HOST=*************
SERVER_USER=jono
SERVER_SSH_KEY_PATH=/home/<USER>/.ssh/id_rsa
# SERVER_SSH_PASSWORD=password

# Network interfaces
WIFI_INTERFACE=wlan0
ETHERNET_INTERFACE=eth0
SERVER_ETHERNET_INTERFACE=enp2s0

# Paths
NGINX_SITES_PATH=/etc/nginx/sites-available
BRIDGE_SCRIPT_PATH=./scripts/restore-bridge.sh
