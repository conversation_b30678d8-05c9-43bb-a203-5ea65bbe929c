# Environment variables for Pi Bridge Admin Panel

# Server configuration
HOST=0.0.0.0
PORT=8000
DEBUG=false

# Authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=your_secure_password_here

# Bridge configuration
BRIDGE_PI_HOST=*************
BRIDGE_PI_USER=jonathan
<PERSON>_PI_SSH_KEY=~/.ssh/id_ed25519
BRIDGE_SERVER_HOST=*************
BRIDGE_SERVER_USER=jono
BRIDGE_SERVER_SSH_KEY=~/.ssh/id_ed25519

# SSH configuration (legacy - use BRIDGE_SERVER_* instead)
SERVER_HOST=*************
SERVER_USER=jono
SERVER_SSH_KEY_PATH=~/.ssh/id_ed25519
# SERVER_SSH_PASSWORD=password

# Network interfaces
WIFI_INTERFACE=wlan0
ETHERNET_INTERFACE=eth0
SERVER_ETHERNET_INTERFACE=enp2s0

# Paths
NGINX_SITES_PATH=/etc/nginx/sites-available
BRIDGE_SCRIPT_PATH=./scripts/restore-bridge.sh
