pi-bridge-admin/README.md
markdown
Copy
Edit
# Pi Bridge Admin Panel

A lightweight self-hosted admin interface for managing and monitoring a Raspberry Pi-based Wi-Fi-to-Ethernet NAT bridge, its connected production server, and associated services.

---

## 🧱 System Overview

This project manages a setup consisting of:

1. **Raspberry Pi (Bridge)**
   - Connects to Wi-Fi (`wlan0`)
   - Bridges traffic to production server via Ethernet (`eth0`)
   - Handles:
     - NAT forwarding
     - Docker container orchestration
     - Nginx proxy configuration
     - Cloudflare tunnel management

2. **Production Server**
   - Headless Linux server connected via Ethernet
   - Hosts production websites via Nginx + Docker
   - Uses static IP: `*************`
   - SSH reachable via the Pi bridge

---

## 🎯 Project Goals

Build a web-based dashboard (FastAPI backend, HTMX + Tailwind frontend) that can:

### ✅ Dashboard View (`/dashboard`)
- Show system health for both Pi and Server:
  - CPU, memory, disk usage
  - Uptime and temperature (on Pi)
- Status of critical services:
  - `cloudflared`, `nginx`, `docker`
- IP status:
  - Current IPs for `wlan0`, `eth0`
  - Ping latency to `*************`

---

### ✅ Bridge/NAT Management (`/bridge`)
- Confirm and toggle:
  - IP forwarding (`/proc/sys/net/ipv4/ip_forward`)
  - MASQUERADE rules in iptables
- Option to re-run bridge setup script (`restore-bridge.sh`)

---

### ✅ Docker Panel (`/docker`)
- List all containers (on Pi and Server)
- Start/stop/restart individual containers
- View logs (stdout/stderr)
- Prune unused containers/images

---

### ✅ Nginx Manager (`/nginx`)
- List `/etc/nginx/sites-available/*.conf`
- Edit config files via web editor
- Test config (`nginx -t`)
- Reload service (`systemctl reload nginx`)

---

### ✅ Cloudflare Tunnel Manager (`/cloudflare`)
- Check tunnel status
- View logs
- Restart `cloudflared`

---

## 📡 Cross-System Monitoring

- Use SSH from Pi → Server to gather stats remotely
- All system-level actions on the server are issued over SSH (as `jono@*************`)
- Provide a `.env` or `config.yaml` to store credentials or load from SSH agent

---

## 🔐 Authentication

- Start with HTTP Basic Auth
- Later consider session-based login or OAuth2 if hosted externally

---

## 🧪 Optional Future Features

- WebSocket-powered live stats
- Alerting/email for service down
- Visual traffic graphs (iptables/netstats)
- Backup config management

---

## 🚀 Setup Instructions (Dev)

```bash
git clone https://github.com/<your-name>/pi-bridge-admin.git
cd pi-bridge-admin

# Create virtualenv
python -m venv venv
source venv/bin/activate

# Install deps
pip install -r requirements.txt

# Run dev server
uvicorn app.main:app --reload
📝 Dev Tools
Python 3.11+

FastAPI

HTMX + Tailwind (served via templates)

SSH via paramiko or subprocess

Docker Python SDK

📍 Notes
Bridge interface: eth0 (Pi) → enp2s0 (Server)

Static IP for Server: *************

If dashboard can’t reach server, fallback to “degraded” mode with Pi-only stats

