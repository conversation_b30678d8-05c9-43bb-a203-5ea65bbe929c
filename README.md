pi-bridge-admin/README.md
markdown
Copy
Edit
# Pi Bridge Admin Panel

A lightweight self-hosted admin interface for managing and monitoring a Raspberry Pi-based Wi-Fi-to-Ethernet NAT bridge, its connected production server, and associated services.

---

## 🧱 System Overview

This project manages a setup consisting of:

1. **Raspberry Pi (Bridge)**
   - Connects to Wi-Fi (`wlan0`)
   - Bridges traffic to production server via Ethernet (`eth0`)
   - Handles:
     - NAT forwarding
     - Docker container orchestration
     - Nginx proxy configuration
     - Cloudflare tunnel management

2. **Production Server**
   - Headless Linux server connected via Ethernet
   - Hosts production websites via Nginx + Docker
   - Uses static IP: `*************`
   - SSH reachable via the Pi bridge

---

## 🎯 Project Goals

Build a web-based dashboard (FastAPI backend, HTMX + Tailwind frontend) that can:

### ✅ Dashboard View (`/dashboard`)
- Show system health for both Pi and Server:
  - CPU, memory, disk usage
  - Uptime and temperature (on Pi)
- Status of critical services:
  - `cloudflared`, `nginx`, `docker`
- IP status:
  - Current IPs for `wlan0`, `eth0`
  - Ping latency to `*************`

---

### ✅ Bridge/NAT Management (`/bridge`)
- Confirm and toggle:
  - IP forwarding (`/proc/sys/net/ipv4/ip_forward`)
  - MASQUERADE rules in iptables
- Option to re-run bridge setup script (`restore-bridge.sh`)

---

### ✅ Docker Panel (`/docker`)
- List all containers (on Pi and Server)
- Start/stop/restart individual containers
- View logs (stdout/stderr)
- Prune unused containers/images

---

### ✅ Nginx Manager (`/nginx`)
- List `/etc/nginx/sites-available/*.conf`
- Edit config files via web editor
- Test config (`nginx -t`)
- Reload service (`systemctl reload nginx`)

---

### ✅ Cloudflare Tunnel Manager (`/cloudflare`)
- Check tunnel status
- View logs
- Restart `cloudflared`

---

## 📡 Cross-System Monitoring

- Use SSH from Pi → Server to gather stats remotely
- All system-level actions on the server are issued over SSH (as `jono@*************`)
- Provide a `.env` or `config.yaml` to store credentials or load from SSH agent

---

## 🔐 Authentication

- Start with HTTP Basic Auth
- Later consider session-based login or OAuth2 if hosted externally

---

## 🧪 Optional Future Features

- WebSocket-powered live stats
- Alerting/email for service down
- Visual traffic graphs (iptables/netstats)
- Backup config management

---

## 🚀 Setup Instructions

### Prerequisites

- Python 3.11+
- Raspberry Pi with Wi-Fi and Ethernet interfaces
- Production server accessible via SSH
- Docker installed on both Pi and server (optional)
- Nginx installed (for web server management)
- Cloudflare tunnel configured (optional)

### Installation

1. **Clone the repository:**
```bash
git clone https://github.com/<your-name>/pi-bridge-admin.git
cd pi-bridge-admin
```

2. **Create and activate virtual environment:**
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies:**
```bash
pip install -r requirements.txt
```

4. **Configure the application:**
```bash
# Copy configuration template
cp config.yaml.example config.yaml
# OR
cp .env.example .env

# Edit configuration with your settings
nano config.yaml  # or .env
```

5. **Set up SSH access to production server:**
```bash
# Generate SSH key if you don't have one
ssh-keygen -t rsa -b 4096 -C "pi-bridge-admin"

# Copy public key to production server
ssh-copy-id jono@*************
```

6. **Make scripts executable:**
```bash
chmod +x scripts/restore-bridge.sh
chmod +x run.py
```

### Running the Application

**Development mode:**
```bash
python run.py
# OR
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**Production mode:**
```bash
# Set debug=false in config.yaml
python run.py
```

### Access the Dashboard

Open your browser and navigate to:
- **Local access:** http://localhost:8000
- **Network access:** http://[pi-ip-address]:8000

**Default credentials:**
- Username: `admin`
- Password: `admin` (change this in config!)

### Initial Bridge Setup

1. **Run the bridge setup script:**
```bash
sudo ./scripts/restore-bridge.sh
```

2. **Or use the web interface:**
   - Go to Bridge/NAT Management
   - Click "Run Bridge Setup Script"
📝 Dev Tools
Python 3.11+

FastAPI

HTMX + Tailwind (served via templates)

SSH via paramiko or subprocess

Docker Python SDK

📍 Notes
Bridge interface: eth0 (Pi) → enp2s0 (Server)

Static IP for Server: *************

If dashboard can’t reach server, fallback to “degraded” mode with Pi-only stats

