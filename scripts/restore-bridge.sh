#!/bin/bash

# Pi Bridge Restore Script
# This script configures the Raspberry Pi as a Wi-Fi to Ethernet bridge

set -e

echo "🌉 Starting Pi Bridge Setup..."

# Configuration
WIFI_INTERFACE="wlan0"
ETHERNET_INTERFACE="eth0"
SERVER_IP="*************"

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   echo "❌ This script must be run as root (use sudo)"
   exit 1
fi

echo "📡 Configuring network interfaces..."

# Enable IP forwarding
echo "✅ Enabling IP forwarding..."
sysctl net.ipv4.ip_forward=1

# Make IP forwarding permanent
if ! grep -q "net.ipv4.ip_forward=1" /etc/sysctl.conf; then
    echo "net.ipv4.ip_forward=1" >> /etc/sysctl.conf
    echo "✅ IP forwarding made permanent"
fi

# Configure iptables for NAT
echo "🔧 Configuring iptables NAT rules..."

# Clear existing rules
iptables -t nat -F POSTROUTING

# Add MASQUERADE rule for Wi-Fi interface
iptables -t nat -A POSTROUTING -o $WIFI_INTERFACE -j MASQUERADE

# Allow forwarding from ethernet to wifi
iptables -A FORWARD -i $ETHERNET_INTERFACE -o $WIFI_INTERFACE -j ACCEPT
iptables -A FORWARD -i $WIFI_INTERFACE -o $ETHERNET_INTERFACE -m state --state RELATED,ESTABLISHED -j ACCEPT

echo "💾 Saving iptables rules..."

# Save iptables rules
if command -v iptables-save >/dev/null 2>&1; then
    iptables-save > /etc/iptables/rules.v4 2>/dev/null || true
fi

# Install iptables-persistent if not present
if ! dpkg -l | grep -q iptables-persistent; then
    echo "📦 Installing iptables-persistent..."
    apt-get update
    DEBIAN_FRONTEND=noninteractive apt-get install -y iptables-persistent
fi

echo "🔍 Testing connectivity to server..."

# Test ping to server
if ping -c 1 -W 5 $SERVER_IP >/dev/null 2>&1; then
    echo "✅ Server ($SERVER_IP) is reachable"
else
    echo "⚠️  Warning: Server ($SERVER_IP) is not reachable"
fi

echo "📊 Current network status:"
echo "Wi-Fi Interface ($WIFI_INTERFACE):"
ip addr show $WIFI_INTERFACE | grep "inet " || echo "  No IP assigned"

echo "Ethernet Interface ($ETHERNET_INTERFACE):"
ip addr show $ETHERNET_INTERFACE | grep "inet " || echo "  No IP assigned"

echo "IP Forwarding: $(cat /proc/sys/net/ipv4/ip_forward)"

echo "NAT Rules:"
iptables -t nat -L POSTROUTING -v -n | grep MASQUERADE || echo "  No MASQUERADE rules found"

echo ""
echo "🎉 Pi Bridge setup completed!"
echo "📝 Summary:"
echo "   - IP forwarding: Enabled"
echo "   - NAT rules: Configured"
echo "   - Bridge: $WIFI_INTERFACE → $ETHERNET_INTERFACE"
echo "   - Target server: $SERVER_IP"
echo ""
echo "🔧 To verify the bridge is working:"
echo "   1. Connect a device to the ethernet port"
echo "   2. Configure it with a static IP in the same subnet as the server"
echo "   3. Test internet connectivity through the Pi"
